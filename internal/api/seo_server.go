package api

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/google/uuid"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	listing_products_v1 "github.com/vendasta/generated-protos-go/listing_products/v1"
	"github.com/vendasta/gosdks/fieldmask"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/openai"
	"github.com/vendasta/gosdks/statsd"
	"github.com/vendasta/gosdks/validation"
	"github.com/vendasta/gosdks/validation/rules"
	"github.com/vendasta/gosdks/verrors"
	"github.com/vendasta/listing-products/internal/constants"
	dataforseocategoriesservice "github.com/vendasta/listing-products/internal/dataforseocategories/service"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"

	//	"github.com/vendasta/listing-products/internal/seo/aioauditresult"
	aioauditresultservice "github.com/vendasta/listing-products/internal/seo/aioauditresult/service"
	"github.com/vendasta/listing-products/internal/seo/keywordinfo"
	keywordinfoservice "github.com/vendasta/listing-products/internal/seo/keywordinfo/service"
	seodata "github.com/vendasta/listing-products/internal/seo/model"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	seoworkflow "github.com/vendasta/listing-products/internal/seo/workflow"
	seosettingsservice "github.com/vendasta/listing-products/internal/seosettings/service"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const businessIdValidationError = "business_id is required"

//go:generate mockgen -destination=mock_seo_server.go -source=seo_server.go -package=api
type IAMService interface {
	AccessAccountGroup(ctx context.Context, accountGroupID string, actions ...iam.Action) error
}

type SEOServer struct {
	service                   seodataservice.Service
	seoSettingsService        seosettingsservice.Service
	listingProfileService     listingprofileservice.Interface
	workflowService           *seoworkflow.Service
	keywordInfoService        keywordinfoservice.Service
	dataForSEOCategoryService dataforseocategoriesservice.Service
	aioAuditResultService     aioauditresultservice.Service
	aioAuditWorkflowService   *seoworkflow.AIOAuditService
	openAI                    openai.Client
}

func NewSEOServer(service seodataservice.Service, workflowService *seoworkflow.Service, seoSettingsService seosettingsservice.Service, listingProfileService *listingprofileservice.Service, keywordInfoService keywordinfoservice.Service, dataForSEOCategoryService dataforseocategoriesservice.Service, aioAuditResultService aioauditresultservice.Service, aioAuditWorkflowService *seoworkflow.AIOAuditService, openAI openai.Client) *SEOServer {
	return &SEOServer{
		service:                   service,
		seoSettingsService:        seoSettingsService,
		workflowService:           workflowService,
		listingProfileService:     listingProfileService,
		keywordInfoService:        keywordInfoService,
		dataForSEOCategoryService: dataForSEOCategoryService,
		aioAuditResultService:     aioAuditResultService,
		aioAuditWorkflowService:   aioAuditWorkflowService,
		openAI:                    openAI,
	}
}

// GetActiveSEOAddons fetches all the active seo keyword add ons
func (s *SEOServer) GetActiveSEOAddons(ctx context.Context, request *listing_products_v1.GetActiveSEOAddonsRequest) (*listing_products_v1.GetActiveSEOAddonsResponse, error) {
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(request.GetBusinessId(), businessIdValidationError)).
		Validate()
	if err != nil {
		return nil, err
	}

	results, err := s.service.GetActiveKeywordAddons(ctx, request.GetBusinessId())
	if err != nil {
		return nil, err
	}

	var addOns []*listing_products_v1.AddonActivation
	for _, addon := range results {
		addOns = append(addOns, &listing_products_v1.AddonActivation{
			BusinessId: addon.BusinessID,
			Status:     addon.Status,
			AppId:      addon.AppID,
			AddonId:    addon.AddonID,
			Count:      addon.Count,
			IsTrial:    addon.IsTrial,
		})
	}

	return &listing_products_v1.GetActiveSEOAddonsResponse{ActiveAddons: addOns}, nil
}

func (s *SEOServer) GetSEOData(ctx context.Context, req *listing_products_v1.GetSEODataRequest) (*listing_products_v1.GetSEODataResponse, error) {
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(req.GetBusinessId(), businessIdValidationError)).
		Validate()
	if err != nil {
		return nil, err
	}
	startDate := timeStampFromProto(req.GetStartDate())
	endDate := timeStampFromProto(req.GetEndDate())

	results, previousResults, err := s.service.GetCurrentAndPreviousData(ctx, req.GetBusinessId(), req.GetKeywords(), startDate, endDate)
	if err != nil {
		return nil, err
	}
	keywordInfo, err := s.keywordInfoService.GetMostRecentData(ctx, req.GetBusinessId(), req.GetKeywords(), startDate, endDate)
	if err != nil {
		return nil, err
	}
	results = combineSEODataAndKeywordInfo(results, keywordInfo)
	previousResults = combineSEODataAndKeywordInfo(previousResults, keywordInfo)

	var current []*listing_products_v1.SEOData
	for _, result := range results {
		current = append(current, result.ToProto())
	}
	var previous []*listing_products_v1.SEOData
	for _, result := range previousResults {
		previous = append(previous, result.ToProto())
	}

	return &listing_products_v1.GetSEODataResponse{Data: current, PreviousData: previous}, nil
}

func (s *SEOServer) GetSEODataSummary(ctx context.Context, req *listing_products_v1.GetSEODataSummaryRequest) (*listing_products_v1.GetSEODataSummaryResponse, error) {
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(req.GetBusinessId(), businessIdValidationError)).
		Validate()
	if err != nil {
		return nil, err
	}
	startDate := timeStampFromProto(req.GetStartDate())
	endDate := timeStampFromProto(req.GetEndDate())

	results, previousResults, err := s.service.GetCurrentAndPreviousData(ctx, req.GetBusinessId(), req.GetKeywords(), startDate, endDate)
	if err != nil {
		return nil, err
	}
	keywordInfo, err := s.keywordInfoService.GetMostRecentData(ctx, req.GetBusinessId(), req.GetKeywords(), startDate, endDate)
	if err != nil {
		return nil, err
	}
	results = combineSEODataAndKeywordInfo(results, keywordInfo)
	previousResults = combineSEODataAndKeywordInfo(previousResults, keywordInfo)
	current := make([]*listing_products_v1.SEODataSummary, 0, len(results))
	for _, result := range results {
		current = append(current, result.ConvSEODataSummaryToProto())
	}
	previous := make([]*listing_products_v1.SEODataSummary, 0, len(previousResults))
	for _, result := range previousResults {
		previous = append(previous, result.ConvSEODataSummaryToProto())
	}

	return &listing_products_v1.GetSEODataSummaryResponse{Data: current, PreviousData: previous}, nil
}

func (s *SEOServer) GetLocalSearchSEOData(ctx context.Context, req *listing_products_v1.GetLocalSearchSEODataRequest) (*listing_products_v1.GetLocalSearchSEODataResponse, error) {
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(req.GetBusinessId(), businessIdValidationError)).
		Validate()
	if err != nil {
		return nil, err
	}
	startDate := timeStampFromProto(req.GetStartDate())
	endDate := timeStampFromProto(req.GetEndDate())
	keywords := []string{req.GetKeyword()}
	results, _, err := s.service.GetCurrentAndPreviousData(ctx, req.GetBusinessId(), keywords, startDate, endDate)
	if err != nil {
		return nil, err
	}
	var localSearchData []*listing_products_v1.LocalSearchData
	var keyword string
	for _, result := range results {
		keyword = result.Keyword
		localSearchData = append(localSearchData, result.LocalSearchesToProto()...)
		break
	}
	return &listing_products_v1.GetLocalSearchSEODataResponse{LocalSearchData: localSearchData, Keyword: keyword}, nil
}

func combineSEODataAndKeywordInfo(seoData []*seodata.SEOData, keywordInfo []*keywordinfo.KeywordInfo) []*seodata.SEOData {
	keywordInfoMap := make(map[string]*keywordinfo.KeywordInfo)
	for _, ki := range keywordInfo {
		keywordInfoMap[ki.Keyword] = ki
	}
	for _, sd := range seoData {
		if ki, ok := keywordInfoMap[sd.Keyword]; ok {
			sd.Difficulty = ki.SearchVolume
			sd.SearchVolume = ki.SearchVolume
			sd.LocationInfo = &seodata.LocationInfo{
				LocationCode: ki.LocationID,
				LocationName: ki.LocationName,
			}
		}
	}
	return seoData
}

func (s *SEOServer) GetSEOSettings(ctx context.Context, req *listing_products_v1.GetSEOSettingsRequest) (*listing_products_v1.SEOSettingsResponse, error) {
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(req.GetBusinessId(), businessIdValidationError)).
		Validate()
	if err != nil {
		return nil, err
	}

	result, err := s.seoSettingsService.Get(ctx, req.GetBusinessId())
	if err != nil {
		return nil, err
	}

	return &listing_products_v1.SEOSettingsResponse{
		BusinessId:          result.BusinessID,
		LocalSearchRadius:   result.LocalSearchRadius,
		FavoriteKeywords:    result.FavoriteKeywords,
		IsFullSearchEnabled: result.IsFullSearchEnabled,
	}, nil
}

func (s *SEOServer) SaveSEOSettings(ctx context.Context, req *listing_products_v1.SaveSEOSettingsRequest) (*empty.Empty, error) {
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(req.GetBusinessId(), businessIdValidationError)).
		Rule(validation.ValueNotNil(req.GetFieldMask(), verrors.InvalidArgument, "field_mask is required")).
		Validate()
	if err != nil {
		return &empty.Empty{}, err
	}

	fieldMask := req.GetFieldMask()

	existingSettings, err := s.seoSettingsService.Get(ctx, req.GetBusinessId())
	if err != nil && !verrors.IsError(verrors.NotFound, err) {
		return &empty.Empty{}, err
	}

	fm := fieldmask.FieldMaskProtoPathsToFieldMask(fieldMask.Paths)

	// We need to trigger the seo workflow for all keywords when the radius is updated
	if existingSettings == nil || (existingSettings.LocalSearchRadius != req.GetLocalSearchRadius() && fm.IsPropertyPresent("local_search_radius")) || (existingSettings.IsFullSearchEnabled != req.GetIsFullSearchEnabled() && fm.IsPropertyPresent("is_full_search_enabled")) {
		s.triggerSEOWorkflowForUpdatedSearchRadius(ctx, req.GetBusinessId())
	}

	return &empty.Empty{}, s.seoSettingsService.Upsert(ctx, req.GetBusinessId(), req.GetLocalSearchRadius(), req.GetFavoriteKeywords(), fieldMask, req.GetIsFullSearchEnabled())
}

func (s *SEOServer) StartLocalSEODataWorkflow(ctx context.Context, req *listing_products_v1.StartLocalSEODataWorkflowRequest) (*empty.Empty, error) {
	val := validation.NewValidator()
	for _, business := range req.GetBusinesses() {
		val.Rule(rules.StringNotEmpty(business.GetBusinessId(), businessIdValidationError))
	}
	err := val.Validate()
	if err != nil {
		return nil, err
	}
	var searchParams []*seoworkflow.KeywordSearchParams
	for _, business := range req.GetBusinesses() {
		searchParams = append(searchParams, &seoworkflow.KeywordSearchParams{
			BusinessID: business.GetBusinessId(),
			Keywords:   business.GetKeywords(),
		})
	}
	date := time.Time{}
	if req.GetDate() != nil {
		date = req.GetDate().AsTime()
	}

	if len(searchParams) == 0 {
		// Start workflows in goroutine, so we don't hit context deadlines while orchestrating accounts
		go func() {
			err := s.workflowService.StartSEOWorkflows(context.Background(), req.GetForceSerpWorkflow(), req.GetForceKeywordInfoWorkflow(), req.GetForceSuggestedKeywordsWorkflow(), req.GetIgnoreDataLakeResults(), date)
			if err != nil {
				logging.Errorf(context.Background(), "error starting seo data workflow: %s", err.Error())
			}
		}()
		return &empty.Empty{}, nil
	}
	var errors []string

	if req.GetForceSerpWorkflow() {
		businessErrors, err := s.workflowService.StartSERPWorkflows(ctx, true, req.GetIgnoreDataLakeResults(), searchParams, date, uuid.New().String())
		if err != nil {
			errors = append(errors, err.Error())
		}
		if len(errors) > 0 {
			errors = append(errors, businessErrors...)
		}
	}
	if req.GetForceKeywordInfoWorkflow() {
		businessErrors, err := s.workflowService.StartKeywordInfoWorkflows(ctx, date, searchParams)
		if err != nil {
			errors = append(errors, err.Error())
		}
		if len(errors) > 0 {
			errors = append(errors, businessErrors...)
		}
	}

	if req.GetForceSuggestedKeywordsWorkflow() {
		businessErrors, err := s.workflowService.StartSuggestedKeywordsWorkflows(ctx, date, searchParams)
		if err != nil {
			errors = append(errors, err.Error())
		}
		if len(errors) > 0 {
			errors = append(errors, businessErrors...)
		}
	}
	if len(errors) > 0 {
		return &empty.Empty{}, verrors.New(verrors.Internal, "Errors starting workflows %#v", errors)
	}
	return &empty.Empty{}, nil
}

func (s *SEOServer) triggerSEOWorkflowForUpdatedSearchRadius(ctx context.Context, listingProfileID string) error {
	// fetch existing rich data from vstore
	lp, err := s.listingProfileService.Get(ctx, listingProfileID, false)
	if err != nil {
		statsd.Incr(constants.CustomSEOWorkflowMetric, []string{"message:error-fetching-listing-profile"}, 1)
		logging.Errorf(ctx, "LB-2885 error fetching listing profile %s: %s", listingProfileID, err.Error())
		return err
	}

	if lp == nil || lp.RichData == nil || len(lp.RichData.SEOKeywords) < 1 {
		logging.Infof(ctx, "No existing keywords to update for %s", listingProfileID)
		return nil
	}

	statsd.Incr(constants.CustomSEOWorkflowMetric, []string{"message:search-radius-update"}, 1)
	businessErrors, err := s.workflowService.StartSERPWorkflows(ctx, true, true, []*seoworkflow.KeywordSearchParams{
		{
			BusinessID: listingProfileID,
			Keywords:   lp.RichData.SEOKeywords,
		},
	}, time.Now(), uuid.New().String())
	if err != nil {
		return err
	}
	if len(businessErrors) > 0 {
		logging.Errorf(ctx, "errors starting SERP workflows: %s", strings.Join(businessErrors, ", "))
	}
	return nil
}

func (s *SEOServer) StartSEOCategoryWorkflow(ctx context.Context, req *listing_products_v1.StartSEOCategoryWorkflowRequest) (*empty.Empty, error) {
	err := validation.NewValidator().
		Rule(rules.AtLeastOneStringRequired(req.GetBusinessIds(), "business_ids is required")).
		Validate()
	if err != nil {
		return nil, err
	}
	errors, err := s.workflowService.StartCategoryWorkflow(ctx, time.Now(), req.GetBusinessIds())
	if err != nil {
		return nil, err
	}
	if len(errors) > 0 {
		return nil, verrors.New(verrors.Internal, "Errors starting workflows %#v", errors)
	}
	return &empty.Empty{}, err
}

func timeStampFromProto(ts *timestamp.Timestamp) time.Time {
	if ts == nil {
		return time.Time{}
	}
	return ts.AsTime()
}

func (s *SEOServer) GetDataForSEOCategory(ctx context.Context, request *listing_products_v1.GetDataForSEOCategoryRequest) (*listing_products_v1.GetDataForSEOCategoryResponse, error) {
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(request.GetBusinessId(), businessIdValidationError)).
		Validate()
	if err != nil {
		return nil, err
	}
	resp, err := s.dataForSEOCategoryService.Get(ctx, request.GetBusinessId())
	if err != nil {
		return nil, err
	}
	return &listing_products_v1.GetDataForSEOCategoryResponse{
		BusinessId:        resp.BusinessID,
		PrimaryCategoryId: resp.PrimaryCategoryID,
		CategoryIds:       resp.CategoryIDs,
		TaskId:            resp.TaskID,
		RawResponse:       resp.RawResponse,
		Created:           timestamppb.New(resp.Created),
		Updated:           timestamppb.New(resp.Updated),
	}, nil
}

type AuditResponse struct {
	Message    string `json:"message"`
	Status     string `json:"status"`
	AuditID    string `json:"audit_id"`
	BusinessID string `json:"business_id"`
}

type WebsiteAnalysis struct {
	HasRobotsTxt bool `json:"has_robots_txt"`
	IsCrawlable  bool `json:"is_crawlable"`
}

// func (s *SEOServer) HandleStartAudit(ctx context.Context, request *listing_products_v1.AuditRequest) (*listing_products_v1.AuditResponse, error) {
// 	if request.BrandName == "" {
// 		return nil, verrors.New(verrors.InvalidArgument, "brand_name is required")
// 	}
// 	if request.WebsiteUrl == "" {
// 		return nil, verrors.New(verrors.InvalidArgument, "website_url is required")
// 	}
// 	if request.BusinessId == "" {
// 		return nil, verrors.New(verrors.InvalidArgument, "business_id is required")
// 	}

// 	logging.Infof(ctx, "Starting analysis for website %s + %s", request.WebsiteUrl, request.BusinessId)

// 	websiteAnalysis, err := s.analyzeWebsite(ctx, request.WebsiteUrl)
// 	if err != nil {
// 		logging.Errorf(ctx, "analysis failed for website %s: %v", request.WebsiteUrl, err)
// 		return nil, nil
// 	}
// 	logging.Infof(ctx, "analysis completed for website %s: robots.txt=%v, crawlable=%v",
// 		request.WebsiteUrl, websiteAnalysis.HasRobotsTxt, websiteAnalysis.IsCrawlable)

// 	if websiteAnalysis.HasRobotsTxt && websiteAnalysis.IsCrawlable {
// 		logging.Infof(ctx, "workflow can be called")
// 		auditDate := time.Now().Format("2006-01-02")
// 		robotsAuditScore, err := s.generateRobotsAuditScore(ctx, request.WebsiteUrl)
// 		logging.Infof(ctx, "robotsAuditScore %#v", robotsAuditScore)
// 		if err != nil {
// 			logging.Errorf(ctx, "failed to generate robots audit score for %s: %v", request.WebsiteUrl, err)
// 			robotsAuditScore = &RobotsAuditScore{
// 				Score:           5,
// 				Summary:         "Unable to generate detailed robots.txt analysis",
// 				Recommendations: "Please manually review the robots.txt file",
// 			}
// 		}
// 		auditPageData := aioauditresult.AuditPageData{
// 			PageURL: request.WebsiteUrl,
// 			PageData: fmt.Sprintf(`{
// 				"business_id": "%s",
// 				"brand_name": "%s",
// 				"website_url": "%s",
// 				"audit_date": "%s",
// 				"start_date": "%s",
// 				"audit_score_results": [
// 					{
// 						"audit_score_scope_name": "robots",
// 						"audit_score_scope_value": %d,
// 						"audit_score_scope_summary": ["%s"],
// 						"audit_score_recommendations": ["%s"]
// 					}
// 				]
// 			}`, request.BusinessId, request.BrandName, request.WebsiteUrl, auditDate, auditDate, robotsAuditScore.Score, robotsAuditScore.Summary, robotsAuditScore.Recommendations),
// 		}
// 		aioAuditResult := aioauditresult.AIOAuditResult{
// 			BusinessID:  request.BusinessId,
// 			BrandName:   request.BrandName,
// 			WebsiteURL:  request.WebsiteUrl,
// 			AuditDate:   auditDate,
// 			AuditStatus: "running",
// 			AuditPages:  []*aioauditresult.AuditPageData{&auditPageData},
// 		}
// 		err = s.aioAuditResultService.UpsertAIOAuditResult(ctx, request.BusinessId, request.WebsiteUrl, auditDate, aioAuditResult)
// 		if err != nil {
// 			logging.Errorf(ctx, "failed to upsert audit page result for %s: %v", request.WebsiteUrl, err)
// 		}
// 		// call workflow here --> StartAIOAuditWorkflow
// 		err = s.aioAuditWorkflowService.StartAIOAuditWorkflow(ctx, request.BusinessId, request.WebsiteUrl, request.BrandName, auditDate)
// 		if err != nil {
// 			logging.Errorf(ctx, "failed to start AIO audit workflow for %s: %v", request.WebsiteUrl, err)
// 			return nil, err
// 		}
// 		auditID := "audit_" + request.BrandName + "_" + uuid.New().String()
// 		logging.Infof(ctx, "Audit analysis started for business %s with audit ID %s", request.BrandName, auditID)
// 		return &listing_products_v1.AuditResponse{
// 			Message:    "Audit analysis started",
// 			Status:     "started",
// 			AuditId:    auditID,
// 			BusinessId: request.BusinessId,
// 		}, nil
// 	} else {
// 		return &listing_products_v1.AuditResponse{
// 			Message: "Crawling Blocked or robots.txt Absent",
// 			Status:  "Pre Requisite Failure",
// 			AuditId: "",
// 		}, nil
// 	}
// }

func (s *SEOServer) analyzeWebsite(ctx context.Context, websiteURL string) (*WebsiteAnalysis, error) {
	prompt := fmt.Sprintf(`Analyze the website at %s and determine:
		1. Whether the website has a robots.txt file accessible at %s/robots.txt
		2. Whether the website is crawlable by web crawlers based on its robots.txt content and other crawlability factors
		
		Please respond with a JSON object containing:
		{
		  "has_robots_txt": true/false,
		  "is_crawlable": true/false,
		  "reasoning": "brief explanation of your analysis"
		}
		
		Note: You should analyze the website structure and accessibility, not just check if robots.txt exists.`, websiteURL, websiteURL)

	messages := []openai.ChatCompletionMessageRequest{
		{
			Role:    string(openai.System),
			Content: "You are a web crawler expert. Analyze websites for crawlability and robots.txt presence. Respond only with valid JSON.",
		},
		{
			Role:    string(openai.User),
			Content: prompt,
		},
	}

	response, err := s.openAI.CreateChatCompletion(ctx, messages,
		openai.WithModel(openai.GPT4Omni),
		//openai.WithMaxTokens(500),
		openai.WithTemperature(0.1),
		openai.WithResponseFormat(openai.ResponseFormatJsonObject))
	if err != nil {
		return nil, fmt.Errorf("failed to call OpenAI API: %w", err)
	}

	var analysis struct {
		HasRobotsTxt bool   `json:"has_robots_txt"`
		IsCrawlable  bool   `json:"is_crawlable"`
		Reasoning    string `json:"reasoning"`
	}

	if err := json.Unmarshal([]byte(response.Content), &analysis); err != nil {
		return nil, fmt.Errorf("failed to parse OpenAI response: %w", err)
	}

	logging.Infof(ctx, "OpenAI analysis for %s: %s", websiteURL, analysis.Reasoning)

	return &WebsiteAnalysis{
		HasRobotsTxt: analysis.HasRobotsTxt,
		IsCrawlable:  analysis.IsCrawlable,
	}, nil
}

type RobotsAuditScore struct {
	Score           int    `json:"score"`
	Summary         string `json:"summary"`
	Recommendations string `json:"recommendations"`
}

func (s *SEOServer) generateRobotsAuditScore(ctx context.Context, websiteURL string) (*RobotsAuditScore, error) {
	prompt := fmt.Sprintf(`Analyze the robots.txt file at %s/robots.txt and provide a comprehensive audit score and recommendations.

Please respond with a JSON object containing:
{
  "score": <number from 1-10>,
  "summary": "brief summary of the robots.txt analysis",
  "recommendations": "specific recommendations for improving the robots.txt file"
}

Consider factors like:
- Proper syntax and formatting
- Appropriate use of User-agent directives
- Correct Allow/Disallow rules
- Sitemap references
- Security considerations
- Crawl-delay settings

Score guidelines:
- 1-3: Poor robots.txt with major issues
- 4-6: Basic robots.txt with room for improvement
- 7-8: Good robots.txt with minor issues
- 9-10: Excellent robots.txt implementation`, websiteURL)

	messages := []openai.ChatCompletionMessageRequest{
		{
			Role:    string(openai.System),
			Content: "You are an SEO expert specializing in robots.txt analysis. Provide accurate, actionable feedback. Respond only with valid JSON.",
		},
		{
			Role:    string(openai.User),
			Content: prompt,
		},
	}
	response, err := s.openAI.CreateChatCompletion(ctx, messages,
		openai.WithModel(openai.GPT4Omni),
		openai.WithTemperature(0.1),
		openai.WithResponseFormat(openai.ResponseFormatJsonObject))
	if err != nil {
		return nil, fmt.Errorf("failed to call OpenAI API for robots audit: %w", err)
	}

	var auditScore RobotsAuditScore
	if err := json.Unmarshal([]byte(response.Content), &auditScore); err != nil {
		return nil, fmt.Errorf("failed to parse OpenAI response for robots audit: %w", err)
	}
	if auditScore.Score < 1 {
		auditScore.Score = 1
	} else if auditScore.Score > 10 {
		auditScore.Score = 10
	}
	logging.Infof(ctx, "Generated robots audit score for %s: %d/10", websiteURL, auditScore.Score)
	return &auditScore, nil
}

func (s *SEOServer) GetAIOAuditStatus(ctx context.Context, request *listing_products_v1.GetAIOAuditStatusRequest) (*listing_products_v1.GetAIOAuditStatusResponse, error) {
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(request.GetWebsiteUrl(), "WebsiteURL is required")).
		Validate()
	if err != nil {
		return nil, err
	}
	date := time.Now().Format("2006-01-02")
	auditStatus, err := s.aioAuditResultService.GetAuditStatus(ctx, request.GetBusinessId(), request.GetWebsiteUrl(), date)
	if err != nil {
		return nil, err
	}

	return &listing_products_v1.GetAIOAuditStatusResponse{
		AuditStatus: string(auditStatus),
	}, nil
}

func (s *SEOServer) GetAllAIOAuditScoreResults(ctx context.Context, request *listing_products_v1.GetAllAIOAuditScoreResultsRequest) (*listing_products_v1.GetAllAIOAuditScoreResultsResponse, error) {
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(request.GetWebsiteUrl(), "WebsiteURL is required")).
		Validate()
	if err != nil {
		return nil, err
	}
	date := time.Now().Format("2006-01-02")
	auditScoreResults, err := s.aioAuditResultService.GetAuditScoreResult(ctx, request.GetBusinessId(), request.GetWebsiteUrl(), date)
	if err != nil {
		return nil, err
	}

	return &listing_products_v1.GetAllAIOAuditScoreResultsResponse{
		AuditScoreResults: auditScoreResults,
	}, nil
}

	func (s *SEOServer) GetAllAIOAudit(ctx context.Context, request *listing_products_v1.GetAllAIOAuditRequest) (*listing_products_v1.GetAllAIOAuditResponse, error) {
		err := validation.NewValidator().
			Rule(rules.StringNotEmpty(request.G, "WebsiteURL is required")).
			Validate()
		if err != nil {
			return nil, err
		}			
		date := time.Now().Format("2006-01-02")
		auditResults, err := s.aioAuditResultService.GetAIOAuditResult(ctx, request.GetBusinessId(), request.GetWebsiteUrl(), date)
		if err != nil {
			return nil, err
		}

		return &listing_products_v1.GetAllAIOAuditResponse{
			Audit: auditResults,
		}, nil
	}

	func (s *SEOServer) GetAIOAudit(ctx context.Context, request *listing_products_v1.GetAIOAuditRequest) (*listing_products_v1.GetAIOAuditResponse, error) {
		err := validation.NewValidator().
			Rule(rules.StringNotEmpty(request.GetWebsiteUrl(), "WebsiteURL is required")).
			Validate()
		if err != nil {
			return nil, err
		}
		date := time.Now().Format("2006-01-02")
		auditResults, err := s.aioAuditResultService.GetAIOAuditResult(ctx, request.GetBusinessId(), request.GetWebsiteUrl(), date)
		if err != nil {
			return nil, err
		}

		return &listing_products_v1.GetAIOAuditResponse{
			Audit: auditResults,
		}, nil
	}

	func (s *SEOServer) GetAIOAuditSummary(ctx context.Context, request *listing_products_v1.GetAIOAuditSummaryRequest) (*listing_products_v1.GetAIOAuditSummaryResponse, error) {
		err := validation.NewValidator().
			Rule(rules.StringNotEmpty(request.GetWebsiteUrl(), "WebsiteURL is required")).
			Validate()
		if err != nil {
			return nil, err
		}
		date := time.Now().Format("2006-01-02")
		auditSummary, err := s.aioAuditResultService.GetAuditSummary(ctx, request.GetBusinessId(), request.GetWebsiteUrl(), date)
		if err != nil {
			return nil, err
		}

		return &listing_products_v1.GetAIOAuditSummaryResponse{
			AuditSummary: auditSummary,
		}, nil
	}

	func (s *SEOServer) GetAIOAuditScoreResults(ctx context.Context, request *listing_products_v1.GetAIOAuditScoreResultsRequest) (*listing_products_v1.GetAIOAuditScoreResultsResponse, error) {
		err := validation.NewValidator().
			Rule(rules.StringNotEmpty(request.GetWebsiteUrl(), "WebsiteURL is required")).
			Validate()
		if err != nil {
			return nil, err
		}
		date := time.Now().Format("2006-01-02")
		auditScoreResults, err := s.aioAuditResultService.GetAuditScoreResult(ctx, request.GetBusinessId(), request.GetWebsiteUrl(), date)
		if err != nil {
			return nil, err
		}

		return &listing_products_v1.GetAIOAuditScoreResultsResponse{
			AuditScoreResults: auditScoreResults,
		}, nil
	}
